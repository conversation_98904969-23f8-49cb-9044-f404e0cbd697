// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fleather/fleather.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/language_toggle.dart';
import 'package:pdl_superapp/components/login/login_header.dart';
import 'package:pdl_superapp/components/normal_carousel.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/auth/login_controller.dart';
import 'package:pdl_superapp/models/flyer_models.dart';
import 'package:pdl_superapp/utils/analytics_utils.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/auth_services.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/import_helper_web/empty_stub.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginPage extends StatelessWidget {
  LoginPage({super.key});

  final LoginController controller = Get.put(LoginController());
  final AuthService _authService = AuthService();

  @override
  Widget build(BuildContext context) {
    // Define breakpoint for wide screens (tablets, desktops)
    // Use MediaQuery to determine screen size for responsive design
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isWideScreen = kIsWeb && screenWidth > 768; // 768px breakpoint

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Obx(
        () =>
            isWideScreen
                ? _buildWebLayout(context)
                : _buildMobileLayout(context),
      ),
    );
  }

  // Mobile layout (original design)
  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      physics: ClampingScrollPhysics(),
      child: Column(
        children: [
          // Head section
          LoginHeader(
            child: Padding(
              padding: const EdgeInsets.only(top: 70),
              child: Obx(() {
                if (controller.state.value == ControllerState.firstLoad) {
                  return Container();
                }
                return NormalCarousel(
                  onPressed: (val) => onTapBanner(context, flyerData: val),
                  arrData: controller.flyerArrData,
                );
              }),
            ),
          ),
          // Body Section
          Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            child: body(context),
          ),
          SizedBox(height: paddingSmall),
          // Info Section
          Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            child: _bottomInfo(context),
          ),
          SizedBox(height: paddingLarge),
        ],
      ),
    );
  }

  // Web layout (based on image)
  Widget _buildWebLayout(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // Responsive card width: smaller on smaller screens
    final cardWidth =
        screenWidth > 1200
            ? screenWidth *
                0.5 // 50% on large screens
            : screenWidth > 900
            ? screenWidth *
                0.7 // 70% on medium screens
            : screenWidth * 0.9; // 90% on smaller screens

    return Stack(
      children: [
        Positioned.fill(
          child: Utils.cachedImageWrapper(
            'image/img-bg-web.png',
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Center(
            child: SizedBox(
              width: cardWidth,
              child: Card(
                elevation: 3,
                color: Theme.of(context).colorScheme.surface,
                child: Padding(
                  padding: const EdgeInsets.all(paddingMedium),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left side - promotional content
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(16),
                              bottomLeft: Radius.circular(16),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Obx(() {
                                if (controller.state.value ==
                                    ControllerState.firstLoad) {
                                  return Container();
                                }
                                return NormalCarousel(
                                  onPressed:
                                      (val) =>
                                          onTapBanner(context, flyerData: val),
                                  arrData: controller.flyerArrData,
                                );
                              }),
                              _bottomInfo(context),
                              // Promotional text
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      // Right side - login form
                      Expanded(child: body(context)),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget body(context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: Get.width,
          padding: EdgeInsets.symmetric(vertical: paddingMedium),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  'text_welcome'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              LanguageToggle(controller: controller.langC),
            ],
          ),
        ),
        SizedBox(height: paddingMedium),
        PdlTextField(
          label: 'label_username'.tr,
          hint: 'hint_username'.tr,
          textController: controller.usernameTextController,
          onChanged: (val) => controller.emptValidator(),
          borderColor: controller.isInvalid.isTrue ? kColorError : null,
          textInputAction: TextInputAction.next,
        ),
        SizedBox(height: paddingLarge),
        PdlTextField(
          label: 'label_password'.tr,
          hint: 'hint_password'.tr,
          isPassword: true,
          textController: controller.passwordTextController,
          onChanged: (val) => controller.emptValidator(),
          onSubmitted: (val) {
            // Handle Enter key press - submit form if valid
            if (controller.isButtonAvailable.isTrue) {
              controller.perform();
            }
          },
          borderColor: controller.isInvalid.isTrue ? kColorError : null,
          textInputAction: TextInputAction.done,
        ),
        SizedBox(height: paddingMedium),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // Track remember me toggle
                  AnalyticsUtils.trackElementClick(
                    elementName: 'Remember Me Checkbox Toggled',
                    elementType: 'Checkbox',
                    section: 'Login Page',
                    action: 'tap',
                    additionalData: {
                      'current_state': controller.rememberMe.value,
                      'new_state': !controller.rememberMe.value,
                    },
                  );
                  controller.toggleRememberMe();
                },
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Obx(
                      () => Icon(
                        controller.rememberMe.value
                            ? Icons.check_box
                            : Icons.check_box_outline_blank,
                        color:
                            controller.rememberMe.value
                                ? Theme.of(context).primaryColor
                                : Color(0xFFD1D1D1),
                      ),
                    ),
                    SizedBox(width: 4),
                    Text('text_remember_me'.tr),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                // Track forgot password link click
                AnalyticsUtils.trackElementClick(
                  elementName: 'Forgot Password Link Clicked',
                  elementType: 'Text Link',
                  section: 'Login Page',
                  action: 'tap',
                  additionalData: {
                    'from_page': 'login',
                    'user_has_credentials': controller.isButtonAvailable.value,
                  },
                );
                Get.toNamed(Routes.FORGOT_PASSWORD);
              },
              child: Text(
                'text_forgot_password'.tr,
                style: Theme.of(
                  context,
                ).textTheme.labelLarge?.copyWith(color: kColorPaninBlue),
              ),
            ),
          ],
        ),

        SizedBox(height: paddingLarge),
        Obx(() {
          if (controller.isInvalid.isFalse) {
            return Container();
          }
          return CommonWidgets.errorCard(
            context,
            content: 'error_label_login_invalid'.tr,
          );
        }),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                width: Get.width,
                child: Obx(
                  () => PdlButton(
                    controller: controller,
                    onPressed:
                        controller.isButtonAvailable.isTrue
                            ? () => controller.perform()
                            : null,
                    title: 'button_login'.tr,
                    analyticsName: 'Login Button Clicked',
                    analyticsSection: 'Login Page',
                    analyticsData: {
                      'remember_me': controller.rememberMe.value,
                      'form_valid': controller.isButtonAvailable.value,
                      'platform': kIsWeb ? 'web' : 'mobile',
                    },
                  ),
                ),
              ),
            ),

            if (!kIsWeb)
              Obx(() {
                if (controller.biometricAvailable.isTrue) {
                  return Padding(
                    padding: EdgeInsets.only(left: paddingSmall),
                    child: GestureDetector(
                      onTap: () async {
                        // Track biometric login attempt
                        AnalyticsUtils.trackElementClick(
                          elementName: 'Biometric Login Button Clicked',
                          elementType: 'Biometric Button',
                          section: 'Login Page',
                          action: 'tap',
                          additionalData: {
                            'biometric_available':
                                controller.biometricAvailable.value,
                            'has_saved_credentials':
                                controller.rememberMe.value,
                            'platform': 'mobile',
                          },
                        );
                        await _onFailedBio(context);
                      },
                      child: Container(
                        padding: EdgeInsets.all(paddingSmall),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          border: Border.all(
                            color:
                                Get.isDarkMode
                                    ? kColorBorderDark
                                    : kColorBorderLight,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Utils.cachedSvgWrapper(
                          'icon/ic-linear-scan-user.svg',
                        ),
                      ),
                    ),
                  );
                }
                return Container();
              }),
          ],
        ),
        SizedBox(height: paddingSmall),
      ],
    );
  }

  Future<void> _onFailedBio(BuildContext context) async {
    bool isAuthenticated = await _authService.authenticateWithBiometrics();
    if (isAuthenticated) {
      showSuccessFailedSign(context);
      // Proceed to the next screen or perform desired action
      Future.delayed(Duration(seconds: 2));
      controller.performLoginBio();
    } else {
      // Show an error message
      showSuccessFailedSign(
        context,
        isFailed: true,
        onTryAgain: () {
          Get.back();
          _onFailedBio(context);
        },
      );
    }
  }

  Widget _bottomInfo(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        RichText(
          text: TextSpan(
            style: Theme.of(context).textTheme.labelLarge,
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text('text_confirmation_1'.tr),
              ),
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: GestureDetector(
                  onTap:
                      () => Get.toNamed(
                        Routes.READ_PAGE,
                        arguments: {'type': kInfoTypeTnc},
                      ),
                  child: Text(
                    'text_confirmation_2'.tr,
                    style: Theme.of(
                      context,
                    ).textTheme.labelLarge?.copyWith(color: kColorPaninBlue),
                  ),
                ),
              ),
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text('text_confirmation_3'.tr),
              ),
            ],
          ),
        ),
        SizedBox(height: paddingLarge),
        Row(
          children: [
            _bottomInfoContainer(
              context,
              onPressed: () async {
                String contact = "6281381267094";
                String text = 'Hello';
                String androidUrl = "whatsapp://send?phone=$contact&text=$text";
                String iosUrl =
                    "https://wa.me/$contact?text=${Uri.parse(text)}";

                String webUrl =
                    'https://api.whatsapp.com/send/?phone=$contact&text=hi';
                try {
                  if (isIOS) {
                    await launchUrl(Uri.parse(iosUrl));
                  } else if (isAndroid) {
                    await launchUrl(Uri.parse(androidUrl));
                  }
                } catch (e) {
                  await launchUrl(
                    Uri.parse(webUrl),
                    mode: LaunchMode.externalApplication,
                  );
                }
              },
              url: 'icon/ic-info-call-center.svg',
              title: 'text_customer_care'.tr,
            ),
            _bottomInfoContainer(
              context,
              url: 'icon/ic-info-contact-us.svg',
              title: 'text_about_us'.tr,
              onPressed:
                  () => Get.toNamed(
                    Routes.READ_PAGE,
                    arguments: {'type': kInfoTypeAboutUs},
                  ),
            ),
          ],
        ),
      ],
    );
  }

  Expanded _bottomInfoContainer(
    BuildContext context, {
    required String url,
    required String title,
    Function()? onPressed,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onPressed,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Utils.cachedSvgWrapper(url),
            SizedBox(height: paddingSmall),
            Text(title, style: Theme.of(context).textTheme.labelMedium),
          ],
        ),
      ),
    );
  }

  onTapBanner(context, {required FlyerModels flyerData}) {
    ParchmentDocument document = ParchmentDocument.fromJson(
      jsonDecode(flyerData.content ?? ''),
    );
    FleatherController jsonController = FleatherController(document: document);
    Widget content = Container(
      constraints: BoxConstraints(maxHeight: Get.height / 1.5, minHeight: 200),
      padding: const EdgeInsets.all(paddingMedium),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: paddingLarge),
            Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
              clipBehavior: Clip.hardEdge,
              child: CachedNetworkImage(
                imageUrl: flyerData.frontImage!,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(height: paddingLarge),
            FleatherEditor(
              controller: jsonController,
              readOnly: true,
              enableInteractiveSelection: false,
            ),
          ],
        ),
      ),
    );
    final bool isWideScreen = kIsWeb;
    isWideScreen
        ? Get.dialog(
          Center(
            child: Material(
              borderRadius: BorderRadius.circular(20),
              child: SizedBox(width: Get.width / 2.4, child: content),
            ),
          ),
        )
        : PdlBottomSheet(content: content, title: '${flyerData.title}');
  }

  void showSuccessFailedSign(
    context, {
    bool isFailed = false,
    VoidCallback? onTryAgain,
  }) {
    String title =
        isFailed
            ? 'fingerprint_failed_title_str'.tr
            : "fingerprint_succes_title_str".tr;
    String subTitle =
        isFailed
            ? 'fingerprint_failed_desc_str'.tr
            : "fingerprint_succes_desc_str".tr;
    String message =
        isFailed
            ? 'fingerprint_failed_desc_2_str'.tr
            : "fingerprint_success_desc_2_str".tr;
    String icon =
        isFailed ? 'icon/ic-dialog-x.svg' : 'icon/ic-success-fingerprint.svg';
    Color textColor = isFailed ? kColorErrorText : kColorGlobalGreen;
    Color bgColor = isFailed ? kColorGlobalBgRed : kColorGlobalBgGreen;

    PdlBottomSheet(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              subTitle,
              style: TextStyle(color: kColorTextTersierLight),
            ),
          ),
          SizedBox(height: 20),
          Utils.cachedSvgWrapper(icon),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: bgColor,
            ),
            margin: EdgeInsets.symmetric(vertical: 10),
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
            width: Get.width,
            child: Text(message, style: TextStyle(color: textColor)),
          ),
          if (isFailed)
            PdlButton(
              title: 'Coba Lagi',
              onPressed: onTryAgain,
              width: double.infinity,
            ),
          SizedBox(height: 50),
        ],
      ),
    );
  }
}
