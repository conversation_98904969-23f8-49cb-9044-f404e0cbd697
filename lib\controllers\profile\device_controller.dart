import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/device_models.dart';
import 'package:pdl_superapp/utils/import_helper_web/empty_stub.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:intl/intl.dart';

class DeviceController extends BaseControllers {
  RxList<DeviceModels> listData = RxList();
  Rx<DeviceModels> activeDevices = DeviceModels().obs;
  @override
  void onInit() {
    super.onInit();
    getDeviceList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    if (requestCode == kReqRevokeDevices) {
      Get.back();
      getDeviceList();
      return;
    }
    parseDataList(response);
  }

  String convertApiDateToHuman(String isoDate) {
    // Parsing dari UTC
    DateTime dateUtc = DateTime.parse(isoDate);

    // Konversi ke waktu lokal (GMT+7)
    DateTime localDate = dateUtc.toLocal().add(Duration(hours: 7));

    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    DateTime inputDay = DateTime(
      localDate.year,
      localDate.month,
      localDate.day,
    );

    Duration diff = today.difference(inputDay);
    String timeString = DateFormat('HH:mm').format(localDate);

    if (diff.inDays <= 0) {
      return '${'today_str'.tr} $timeString WIB';
    } else if (diff.inDays < 7) {
      return '${diff.inDays} ${'day_str'.tr}';
    } else if (diff.inDays < 30) {
      int weeks = (diff.inDays / 7).floor();
      return '$weeks ${'week_str'.tr}';
    } else {
      int months = (diff.inDays / 30).floor();
      return '$months ${'label_month'.tr}';
    }
  }

  // @override
  // void loadFailed({required int requestCode, required Response response}) {
  //   super.loadFailed(requestCode: requestCode, response: response);
  //   print('load failed');
  // }

  getDeviceList() async {
    await api.getDeviceList(controllers: this);
  }

  parseDataList(response) async {
    listData.clear();
    if (response != null) {
      for (int i = 0; i < response.length; i++) {
        DeviceModels data = DeviceModels.fromJson(response[i]);
        listData.add(data);
      }
    }
    // get current id
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceId = '';
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;
      deviceId = '${webBrowserInfo.appCodeName}';
    }
    if (isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidDeviceInfo.id;
    }
    if (isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfoPlugin.iosInfo;
      deviceId = '${iosDeviceInfo.identifierForVendor}';
    }
    for (int i = 0; i < listData.length; i++) {
      if (listData[i].deviceId == deviceId) {
        // add current active from list to currentActive
        activeDevices.value = listData[i];
        // remove from list data
        listData.remove(listData[i]);
      }
    }
  }

  performRevokeDevice({required String id}) async {
    await api.performRevokeDevice(
      controllers: this,
      deviceId: Uri.encodeFull(id),
      code: kReqRevokeDevices,
    );
  }
}
