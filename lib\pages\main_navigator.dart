import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_appbar.dart';
import 'package:pdl_superapp/components/pdl_showcase.dart';
import 'package:pdl_superapp/controllers/main_controller.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/profile/profile_page.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/import_helper_web/empty_stub.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:showcaseview/showcaseview.dart';

import '../controllers/home_widget/appbar_controller.dart';

class MainNavigatorScreen extends StatelessWidget {
  final _scrollAppBarController = Get.put(ScrollAppBarController());
  // ignore: unused_field
  final controller = Get.put(MainController());

  // Network controller untuk mengecek status koneksi
  final NetworkController? networkController;

  MainNavigatorScreen({super.key})
    : networkController =
          !kIsWeb
              ? (() {
                try {
                  return Get.find<NetworkController>();
                } catch (e) {
                  // Fallback jika belum diinisialisasi
                  return Get.put(NetworkController());
                }
              })()
              : null;

  final List<Widget> pages = [
    HomePage(),
    Container(),
    Container(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    // Use MediaQuery to determine screen size for responsive design
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isWideScreen = kIsWeb && screenWidth > 768; // 768px breakpoint

    return ShowCaseWidget(
      enableAutoScroll: true,
      builder: (context) {
        showCaseContext = context;
        return Obx(
          () => Scaffold(
            backgroundColor:
                isWideScreen ? Theme.of(context).colorScheme.secondary : null,
            appBar:
                (isWideScreen == false && controller.selectedIndex.value != 0)
                    ? null
                    : PdlAppbar(
                      titles:
                          controller.selectedIndex.value == 0
                              ? null
                              : getTitle(),
                      bgColor: kColorPaninBlue,
                      tutorActionOne: controller.tutorBeranda[0],
                      tutorActionTwo: controller.tutorBeranda[1],
                      total: controller.tutorBeranda.length,
                    ),
            extendBodyBehindAppBar: true,
            body: Row(
              children: [
                if (isWideScreen)
                  Expanded(
                    flex: 2,
                    child: SafeArea(
                      top: false,
                      bottom: true,
                      child: Container(
                        width: Get.width,
                        height: Get.height,
                        color: Theme.of(context).colorScheme.surface,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(height: 70),
                            _navItemWeb(
                              context,
                              title: 'menu_title_home'.tr,
                              index: 0,
                              image: 'icon/homepage.svg',
                            ),
                            _navItemWeb(
                              context,
                              title: 'menu_title_leaderboard'.tr,
                              index: 1,
                              image: 'icon/peringkat-top-rank.svg',
                            ),
                            Container(
                              width: Get.width,
                              padding: EdgeInsets.all(paddingMedium),
                              child: Row(
                                children: [
                                  Obx(() {
                                    // Tentukan warna berdasarkan status koneksi
                                    final bool isConnected =
                                        networkController?.isConnected.value ??
                                        true;
                                    final List<Color> gradientColors =
                                        isConnected
                                            ? [
                                              Color(0xFFF0F8FF),
                                              Color(0xFFA6CBEC),
                                            ]
                                            : [
                                              kColorBorderLight,
                                              kColorBorderDark,
                                            ];

                                    return Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(32),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: gradientColors,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withValues(alpha: 0.4),
                                            blurRadius: 10,
                                            // Softness of the shadow
                                            spreadRadius: 0,
                                            // How much the shadow spreads
                                            offset: Offset(
                                              0,
                                              0,
                                            ), // Shadow position
                                          ),
                                        ],
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.all(5),
                                        child: Utils.cachedImageWrapper(
                                          'connected.png',
                                        ),
                                      ),
                                    );
                                  }),
                                  SizedBox(width: paddingSmall),
                                  Expanded(
                                    child: Text(
                                      'Connected',
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelLarge
                                          ?.copyWith(color: kColorTextTersier),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            _navItemWeb(
                              context,
                              title: 'menu_title_info'.tr,
                              index: 2,
                              image: 'icon/berita-informasi.svg',
                            ),
                            _navItemWeb(
                              context,
                              title: 'menu_title_profile'.tr,
                              index: 3,
                              image: 'icon/profile-saya.svg',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                if (isWideScreen) SizedBox(width: paddingMedium),
                Expanded(
                  flex: 7,
                  child: Container(
                    width: Get.width,
                    height: Get.height,
                    color: Theme.of(context).colorScheme.surface,
                    child: pages[controller.selectedIndex.value],
                  ),
                ),
                if (isWideScreen) SizedBox(width: paddingMedium),
                if (isWideScreen)
                  Expanded(
                    flex: controller.selectedIndex.value == 0 ? 4 : 2,
                    child: Container(
                      width: Get.width,
                      height: Get.height,
                      color: Theme.of(context).colorScheme.surface,
                    ),
                  ),
              ],
            ),

            bottomNavigationBar:
                isWideScreen
                    ? null
                    : PdlShowcase(
                      globalKey: controller.tutorBeranda.last.key,
                      title: controller.tutorBeranda.last.title,
                      description: controller.tutorBeranda.last.description,
                      number: controller.tutorBeranda.last.number,
                      total: controller.tutorBeranda.length,
                      child: Container(
                        width: Get.width,
                        height: 90,
                        padding: EdgeInsets.only(
                          bottom: isIOS ? paddingMedium : 0,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(149, 157, 165, 0.2),
                              blurRadius: 24,
                              spreadRadius: 0,
                              offset: Offset(0, 2),
                            ),
                          ],
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(40),
                            topRight: Radius.circular(40),
                          ),
                        ),
                        child: Container(
                          width: Get.width,
                          padding: EdgeInsets.symmetric(
                            horizontal: paddingMedium,
                          ),
                          child: Obx(
                            () => Row(
                              children: [
                                _navItem(
                                  context,
                                  title: 'menu_title_home'.tr,
                                  index: 0,
                                  image: 'icon/homepage.svg',
                                ),
                                _navItem(
                                  context,
                                  title: 'menu_title_leaderboard'.tr,
                                  index: 1,
                                  image: 'icon/peringkat-top-rank.svg',
                                ),
                                Expanded(
                                  child: Center(
                                    child: Obx(() {
                                      // Tentukan warna berdasarkan status koneksi
                                      final bool isConnected =
                                          networkController
                                              ?.isConnected
                                              .value ??
                                          true;
                                      final List<Color> gradientColors =
                                          isConnected
                                              ? [kColorError, kColorGlobalRed]
                                              : [
                                                kColorBorderLight,
                                                kColorBorderDark,
                                              ];

                                      return Container(
                                        width: 54,
                                        height: 54,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            54,
                                          ),
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: gradientColors,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Color(
                                                0xFF8FD8FF,
                                              ).withValues(alpha: 0.4),
                                              blurRadius:
                                                  10, // Softness of the shadow
                                              spreadRadius:
                                                  0, // How much the shadow spreads
                                              offset: Offset(
                                                0,
                                                0,
                                              ), // Shadow position
                                            ),
                                          ],
                                        ),
                                        child: Padding(
                                          padding: EdgeInsets.all(10),
                                          child: Utils.cachedImageWrapper(
                                            'connected.png',
                                          ),
                                          // Image.asset(
                                          //   'assets/connected.png',
                                          // ),
                                        ),
                                      );
                                    }),
                                  ),
                                ),
                                _navItem(
                                  context,
                                  title: 'menu_title_info'.tr,
                                  index: 2,
                                  image: 'icon/berita-informasi.svg',
                                ),
                                _navItem(
                                  context,
                                  title: 'menu_title_profile'.tr,
                                  index: 3,
                                  image: 'icon/profile-saya.svg',
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
          ),
        );
      },
    );
  }

  String getTitle() {
    String result = '';
    switch (controller.selectedIndex.value) {
      case 1:
        result = 'menu_title_leaderboard'.tr;
        break;
      case 2:
        result = 'menu_title_info'.tr;
        break;
      case 3:
        result = 'menu_title_profile'.tr;
        break;
      default:
    }

    return result;
  }

  Expanded _navItem(
    context, {
    required int index,
    required String image,
    required String title,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (index == 0) {
            _scrollAppBarController.handleScroll(0);
          }
          controller.selectedIndex.value = index;
        },
        child: Container(
          color: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Utils.cachedSvgWrapper(
                image,
                width: 30,
                height: 30,
                color:
                    index == controller.selectedIndex.value
                        ? Theme.of(context).primaryColor
                        : null,
                fit: BoxFit.contain,
              ),
              SizedBox(height: paddingSmall),
              FittedBox(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color:
                        index == controller.selectedIndex.value
                            ? Theme.of(context).primaryColor
                            : kColorTextTersier,
                    fontWeight:
                        index == controller.selectedIndex.value
                            ? FontWeight.w700
                            : FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _navItemWeb(
    context, {
    required int index,
    required String image,
    required String title,
  }) {
    return GestureDetector(
      onTap: () => controller.selectedIndex.value = index,
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingMedium,
        ),
        child: Row(
          children: [
            Utils.cachedSvgWrapper(
              image,
              width: 32,
              color:
                  index == controller.selectedIndex.value
                      ? Theme.of(context).primaryColor
                      : null,
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color:
                      index == controller.selectedIndex.value
                          ? Theme.of(context).primaryColor
                          : kColorTextTersier,
                  fontWeight:
                      index == controller.selectedIndex.value
                          ? FontWeight.w700
                          : FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
