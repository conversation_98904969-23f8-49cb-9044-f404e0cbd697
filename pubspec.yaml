name: pdl_superapp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+139

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # flutter_flavorizr:
  #   git:
  #     url: https://github.com/wjlee611/flutter_flavorizr.git
  #     ref: feat/migrate-3-29
  get: ^4.7.2
  get_storage: ^2.1.1
  flex_color_scheme: ^8.2.0
  firebase_core: ^3.12.1
  firebase_core_web: ^2.21.1
  firebase_messaging: ^15.1.6
  firebase_messaging_web: ^3.9.6
  firebase_analytics: ^11.4.0
  firebase_analytics_web: ^0.5.10+10
  flutter_local_notifications: ^18.0.1
  cloud_firestore: ^5.6.5
  animated_custom_dropdown: ^3.1.1
  firebase_auth: ^5.5.1
  firebase_auth_web: ^5.14.1
  shorebird_code_push: ^2.0.4
  restart_app: ^1.1.2
  device_preview: ^1.2.0
  shimmer: ^3.0.0
  cached_network_svg_image: ^1.2.0
  cached_network_image: ^3.4.1
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.17
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.2.1
  local_auth: ^2.3.0
  crypto: ^3.0.3
  encrypt: ^5.0.3
  logger: ^2.5.0
  flutter_dotenv: ^5.2.1
  shared_preferences: ^2.5.2
  cached_network_image_platform_interface: ^4.1.1
  qr_flutter: ^4.1.0
  device_info_plus: ^11.3.3
  package_info_plus: ^8.3.0
  app_links: ^6.4.0
  image_picker: ^1.1.2
  dotted_border: ^2.1.0
  fleather: ^1.20.1
  showcaseview: ^4.0.1
  url_launcher: ^6.3.1
  intl: ^0.20.2
  connectivity_plus: ^5.0.2
  fl_chart: ^0.71.0
  camera: ^0.11.1
  image_cropper: ^9.1.0
  path_provider: ^2.1.5
  path: ^1.9.1
  google_mlkit_text_recognition: ^0.15.0
  google_mlkit_object_detection: ^0.15.0
  expandable_page_view: ^1.0.17
  uuid: ^4.3.3
  image: ^4.5.4
  string_similarity: ^2.1.1
  equatable: ^2.0.5
  signature: ^6.0.0
  share_plus: ^11.0.0
  no_screenshot: ^0.3.1
  flutter_inappwebview: ^6.1.5
  flutter_html: ^3.0.0
  http: ^1.4.0
  flutter_secure_storage: ^9.2.4
  fbroadcast: ^2.0.0
  flutter_launcher_icons: ^0.14.4
  internet_connection_checker: ^1.0.0+1
  visibility_detector: ^0.4.0+2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/icon/
    - assets/images/
    - assets/custom_models/
    - shorebird.yaml
    - config/
    - config/development/
    - config/production/
    - .env

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
