import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/utils/keys.dart';

class NotificationDetailController extends BaseControllers {
  String id = '0';
  Rx<InboxModel> data = InboxModel().obs;
  int _completedRequests = 0;
  final int _totalRequests = 2;

  @override
  void onInit() {
    super.onInit();
    // Try to get ID from URL parameters first, then fallback to arguments
    id = Get.parameters['id'] ?? (Get.arguments?['id'] ?? '').toString();
    load();
  }

  @override
  void load() {
    super.load();
    setLoading(true);
    _completedRequests = 0;
    api.getDetailInbox(controllers: this, id: id, code: kReqDetailInbox);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    _completedRequests++;

    switch (requestCode) {
      case kReqDetailInbox:
        data.value = InboxModel.fromJson(response);
        break;
      case kReqReadInbox:
        // Mark notification as read locally
        if (data.value.id != null) {
          data.value.isRead = true;
          data.refresh();
        }
        break;
      default:
    }

    // Only stop loading when all requests are completed
    if (_completedRequests >= _totalRequests) {
      setLoading(false);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    _completedRequests++;

    // Only stop loading when all requests are completed (success or failed)
    if (_completedRequests >= _totalRequests) {
      setLoading(false);
    }
  }
}
