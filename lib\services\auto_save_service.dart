import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';

class AutoSaveService extends GetxService {
  late FormFirestoreService _firestoreService;

  @override
  Future<void> onInit() async {
    super.onInit();
    _firestoreService = FormFirestoreService();
  }

  /// Auto-save form after photo upload using form UUID
  Future<bool> autoSaveFormAfterPhotoUpload({
    required String formId,
    required String photoType,
    required String photoUrl,
  }) async {
    try {
      // Get existing form data from Firestore
      final existingForm = await _firestoreService.getRecruitmentForm(formId);
      if (existingForm == null) {
        return false;
      }

      // Update the specific photo URL in the form data
      final updatedForm = _updatePhotoUrlInForm(
        existingForm,
        photoType,
        photoUrl,
      );

      // Save updated form back to Firestore
      final saveResult = await _firestoreService.saveRecruitmentForm(
        updatedForm,
        formId,
        isAutoSave: true,
      );

      if (saveResult) {
        log('Auto-saved form $formId after uploading $photoType');
      }

      return saveResult;
    } catch (e) {
      log('Error auto-saving form $formId: $e');
      return false;
    }
  }

  /// Update photo URL in form model based on photo type
  RecruitmentFormModel _updatePhotoUrlInForm(
    RecruitmentFormModel form,
    String photoType,
    String photoUrl,
  ) {
    switch (photoType) {
      case 'ktp':
        return form.copyWith(
          ktpImageUrl: photoUrl,
          lastUpdated: DateTime.now().millisecondsSinceEpoch,
        );
      case 'selfie-ktp':
        return form.copyWith(
          selfieKtpImageUrl: photoUrl,
          lastUpdated: DateTime.now().millisecondsSinceEpoch,
        );
      case 'pas-foto':
        return form.copyWith(
          pasFotoImageUrl: photoUrl,
          lastUpdated: DateTime.now().millisecondsSinceEpoch,
        );
      default:
        return form.copyWith(
          lastUpdated: DateTime.now().millisecondsSinceEpoch,
        );
    }
  }

  /// Batch auto-save multiple photos for a form
  Future<bool> batchAutoSavePhotos({
    required String formId,
    required Map<String, String> photoUrls, // photoType -> photoUrl
  }) async {
    try {
      // Get existing form data
      final existingForm = await _firestoreService.getRecruitmentForm(formId);
      if (existingForm == null) {
        return false;
      }

      // Update all photo URLs
      RecruitmentFormModel updatedForm = existingForm;
      for (final entry in photoUrls.entries) {
        updatedForm = _updatePhotoUrlInForm(
          updatedForm,
          entry.key,
          entry.value,
        );
      }

      // Save updated form
      final saveResult = await _firestoreService.saveRecruitmentForm(
        updatedForm,
        formId,
        isAutoSave: true,
      );

      if (saveResult) {
        log('Batch auto-saved form $formId with ${photoUrls.length} photos');
      }

      return saveResult;
    } catch (e) {
      log('Error batch auto-saving form $formId: $e');
      return false;
    }
  }

  /// Check if form exists and can be auto-saved
  Future<bool> canAutoSaveForm(String formId) async {
    try {
      final form = await _firestoreService.getRecruitmentForm(formId);
      if (form == null) {
        return false;
      }

      // Don't auto-save if form is already submitted
      if (form.isSubmitted == true) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get form status for debugging
  Future<Map<String, dynamic>?> getFormStatus(String formId) async {
    try {
      final form = await _firestoreService.getRecruitmentForm(formId);
      if (form == null) return null;

      return {
        'formId': formId,
        'isSubmitted': form.isSubmitted ?? false,
        'formStatus': form.formStatus ?? 'draft',
        'hasServerUuid': form.hasServerUuid ?? false,
        'lastUpdated': form.lastUpdated,
        'ktpImageUrl': form.ktpImageUrl?.isNotEmpty == true,
        'selfieKtpImageUrl': form.selfieKtpImageUrl?.isNotEmpty == true,
        'pasFotoImageUrl': form.pasFotoImageUrl?.isNotEmpty == true,
      };
    } catch (e) {
      return null;
    }
  }

  /// Force refresh form data from server (if online)
  Future<RecruitmentFormModel?> refreshFormData(String formId) async {
    try {
      return await _firestoreService.getRecruitmentForm(formId);
    } catch (e) {
      return null;
    }
  }
}
