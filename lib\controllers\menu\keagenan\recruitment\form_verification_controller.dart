import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/services/background_upload_service.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/utils/form_validation.dart';
import 'package:pdl_superapp/utils/image_file_helper.dart';

class FormVerificationController extends BaseControllers {
  final RecruitmentFormController baseController;

  FormVerificationController({required this.baseController});

  List<String> roleCandidate = ['BP', 'BM', 'BD'];

  // Pending photo service for tracking local photos
  PendingPhotoService? _pendingPhotoService;

  // Form Field data - Images (File on mobile, XFile on web)
  Rx<dynamic> ktpImage = Rx<dynamic>(null);
  Rx<dynamic> selfieKtpImage = Rx<dynamic>(null);
  Rx<dynamic> pasFotoImage = Rx<dynamic>(null);

  // Image URLs from upload
  RxString ktpUrl = ''.obs;
  RxString selfieKtpUrl = ''.obs;
  RxString pasFotoUrl = ''.obs;

  // Upload progress indicators
  RxDouble ktpUploadProgress = 0.0.obs;
  RxDouble selfieKtpUploadProgress = 0.0.obs;
  RxDouble pasFotoUploadProgress = 0.0.obs;

  // Upload status
  RxBool isKtpUploading = false.obs;
  RxBool isSelfieKtpUploading = false.obs;
  RxBool isPasFotoUploading = false.obs;

  // Form Verification - menggunakan RxString untuk recruiter info
  final recruiterName = RxString('');
  final recruiterId = RxString('');
  final recruiterBranch = RxString('');
  final recruiterCode = RxString('');
  final recruiterLevel = RxString('');
  final recruiterPhoto = RxString('');
  final candidateLevelController = TextEditingController();
  final candidateBranchController = TextEditingController();
  final candidateBranchCode = RxInt(0);
  final candidateBranchText = RxString(''); // Reactive text for branch field

  // BranchList
  RxList<BranchModels> branchList = RxList();
  RxList<BranchModels> allBranchList =
      RxList(); // Store all branch data for offline filtering
  RxBool isAgentLoading = false.obs;

  // Validation errors
  RxString ktpImageError = ''.obs;
  RxString selfieKtpImageError = ''.obs;
  RxString pasFotoImageError = ''.obs;
  RxString candidateLevelError = ''.obs;
  RxString candidateBranchError = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    // Initialize pending photo service
    _initializePendingPhotoService();

    // Setup form change listeners
    _setupFormChangeListeners();

    // setup roles candidate yang bisa di pilih
    // BP => BP
    // BM => BP, BM
    // BD up => BP, BM, BD
    String recruiterLevel =
        baseController.prefs.getString(kStorageUserLevel) ?? '';
    switch (recruiterLevel) {
      case kLevelBP:
        roleCandidate.retainWhere((item) => item == 'BP');
        break;
      case kLevelBM:
        roleCandidate.retainWhere((item) => item == 'BP' || item == 'BM');
        break;
      default:
    }

    api.getComboCategoryById(
      controllers: this,
      key: 'AgentLevel',
      code: kReqGetComboBoxLevel,
    );

    // Initialize branch data for offline mode - fetch all branches
    _initializeBranchData();
  }

  /// Initialize pending photo service
  void _initializePendingPhotoService() {
    try {
      _pendingPhotoService = Get.find<PendingPhotoService>();
    } catch (e) {
      log('PendingPhotoService not found, photo tracking will be limited');
    }
  }

  /// Debug method to check service status
  void debugServiceStatus() {
    try {
      final pendingService = Get.find<PendingPhotoService>();
      // Check pending photos count
      pendingService.pendingPhotos.length;
    } catch (e) {
      // PendingPhotoService not found globally
    }

    try {
      final uploadService = Get.find<BackgroundUploadService>();
      // Check upload service status
      uploadService.isBackgroundUploadActive;
    } catch (e) {
      // BackgroundUploadService not found
    }
  }

  /// Force check and upload pending photos for debugging
  void forceUploadCheck() {
    try {
      final uploadService = Get.find<BackgroundUploadService>();
      uploadService.getDetailedStatus();
      uploadService.forceUploadCheck();
    } catch (e) {
      // Error in force upload check
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetBranch:
        parseDataBranch(response);
        break;
      case kReqGetAllBranch:
        parseAllBranchData(response);
        break;
      default:
    }
  }

  void parseDataBranch(response) {
    final branches =
        (response['content'] as List)
            .map((item) => BranchModels.fromJson(item))
            .toList();

    branchList.clear();
    branchList.assignAll(branches);
    isAgentLoading.value = false;
    log('Branch list loaded: ${branchList.length} items');
  }

  void parseAllBranchData(response) {
    final branches =
        (response['content'] as List)
            .map((item) => BranchModels.fromJson(item))
            .toList();

    allBranchList.clear();
    allBranchList.assignAll(branches);

    // Don't populate the display list initially - keep it empty until user types
    branchList.clear();

    isAgentLoading.value = false;
    log('All branch data loaded: ${allBranchList.length} items');
  }

  // Muat data recruiter dari SharedPreferences
  void loadRecruiterDataFromPrefs() {
    // for Agent with code
    if (recruiterName.value.isEmpty) {
      recruiterName.value =
          baseController.prefs.getString(kStorageAgentName) ?? '';
      recruiterId.value = baseController.prefs.getString(kStorageUserId) ?? '';
      recruiterCode.value =
          baseController.prefs.getString(kStorageAgentCode) ?? '';
      recruiterBranch.value =
          baseController.prefs.getString(kStorageAgentBranch) ?? '';
      recruiterLevel.value =
          baseController.prefs.getString(kStorageUserLevel) ?? '';

      if (recruiterLevel.value == '-') {
        // handle staff level
        recruiterLevel.value =
            (baseController.prefs.getString(kStorageUserLevelComplete) ?? '-')
                .replaceAll('ROLE_AGE_', '');
      }
    }
  }

  // Initialize branch data for offline mode - fetch all branches
  void _initializeBranchData() {
    isAgentLoading.value = true;
    // Fetch all branches with empty branchName parameter
    api.getBranch(
      controllers: this,
      code: kReqGetAllBranch,
      params: "branchName=&size=999",
    );
  }

  // Branch onTextUpdate - now filters locally instead of making API calls
  void onBranchTextChanged(String value) {
    // Update reactive text immediately for UI reactivity
    candidateBranchText.value = value;

    // Clear previous branch error when user starts typing
    candidateBranchError.value = '';

    Future.delayed(Duration(milliseconds: 200)).then((val) {
      if (value.isNotEmpty) {
        // Filter from local data instead of making API call
        _filterBranchesLocally(value);
      } else {
        // Clear branch list when search is empty (hide dropdown)
        branchList.clear();
        // Only reset branch code when text is actually cleared by user
        if (candidateBranchController.text.isEmpty) {
          candidateBranchCode.value = 0;
        }
      }
    });
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    isAgentLoading.value = false;
    if (e == kNoCachedDataMsg) {
      Utils.popup(
        body: 'Tidak ada cached data tersedia untuk data branch',
        type: kPopupFailed,
      );
    }
  }

  // Filter branches locally based on search query
  void _filterBranchesLocally(String query) {
    if (allBranchList.isEmpty) {
      // If no data loaded yet, try to initialize
      _initializeBranchData();
      return;
    }

    final filteredBranches =
        allBranchList.where((branch) {
          final branchName = branch.branchName?.toLowerCase() ?? '';
          return branchName.contains(query.toLowerCase());
        }).toList();

    branchList.clear();
    branchList.assignAll(filteredBranches);

    // Check if current selected branch is still valid after filtering
    _validateCurrentBranchSelection(query, filteredBranches);

    log('Filtered branches: ${branchList.length} items for query: $query');
  }

  // Validate if current branch selection is still valid after filtering
  void _validateCurrentBranchSelection(
    String query,
    List<BranchModels> filteredBranches,
  ) {
    // If we have a current branch code and the text matches exactly with a branch name
    if (candidateBranchCode.value != 0 &&
        candidateBranchController.text.isNotEmpty) {
      // Find if the current branch text exactly matches any branch in ALL branches (not just filtered)
      BranchModels? exactMatchInAll;
      try {
        exactMatchInAll = allBranchList.firstWhere(
          (branch) =>
              branch.branchName?.toLowerCase() ==
                  candidateBranchController.text.toLowerCase() &&
              branch.id == candidateBranchCode.value,
        );
      } catch (e) {
        exactMatchInAll = null;
      }

      // If exact match found in all branches with matching code, keep it
      if (exactMatchInAll != null) {
        // Current selection is still valid, clear any error
        candidateBranchError.value = '';
        return;
      }

      // Only reset branch code if the text has actually changed from the original branch name
      // Check if current text matches the original selected branch
      BranchModels? originalBranch;
      try {
        originalBranch = allBranchList.firstWhere(
          (branch) => branch.id == candidateBranchCode.value,
        );
      } catch (e) {
        originalBranch = null;
      }

      // If user is typing something different from the original branch name, reset
      if (originalBranch == null ||
          originalBranch.branchName?.toLowerCase() !=
              candidateBranchController.text.toLowerCase()) {
        candidateBranchCode.value = 0;
        log(
          'Branch code reset due to text change from original: ${originalBranch?.branchName} to: ${candidateBranchController.text}',
        );
      }
    }
  }

  Future<void> pickKtpImage(String title, String type) async {
    final result = await Get.toNamed(
      Routes.PHOTO_PAGE_PANDUAN,
      parameters: {'galeryDisabled': 'true', 'title': title, 'type': type},
    );
    if (result != null) {
      try {
        dynamic fileToUse;

        // Handle file renaming based on platform
        if (kIsWeb) {
          // On web, use the result as-is (should be XFile)
          fileToUse = result;
          log("Web platform: Using original file without renaming");
        } else {
          // On mobile platforms, rename file as usual (should be File)
          fileToUse = await changeFileNameOnly(
            result,
            'foto-${title.replaceAll(' ', '-').toLowerCase()}-${Utils.getRandomString(length: 6)}.jpg',
          );
          log("File berhasil di-rename: ${fileToUse.path}");
        }

        // Simpan file yang sudah diproses
        if (type == kPhotoTypeKtp) {
          ktpImage.value = fileToUse;
          try {
            baseController.processKtpOcr(fileToUse);
          } catch (ocrError) {
            log('OCR processing failed: $ocrError');
          }
        } else if (type == kPhotoTypeSelfieKtp) {
          selfieKtpImage.value = fileToUse;
        } else if (type == kPhotoTypePasFoto) {
          pasFotoImage.value = fileToUse;
        }

        // Upload image setelah berhasil diproses
        await uploadImage(fileToUse, type);

        // Notify parent controller about form change
        try {
          baseController.onFormChanged();
        } catch (e) {
          log('Parent controller not found: $e');
        }
      } catch (e) {
        log("Error saat memproses file: $e");
        // Jika terjadi error, tetap gunakan file asli
        if (type == kPhotoTypeKtp) {
          ktpImage.value = result;
          try {
            baseController.processKtpOcr(result);
          } catch (ocrError) {
            log('OCR processing failed: $ocrError');
          }
        } else if (type == kPhotoTypeSelfieKtp) {
          selfieKtpImage.value = result;
        } else if (type == kPhotoTypePasFoto) {
          pasFotoImage.value = result;
        }

        // Upload image meskipun processing gagal
        await uploadImage(result, type);

        // Notify parent controller about form change
        try {
          baseController.onFormChanged();
        } catch (e) {
          log('Parent controller not found: $e');
        }
      }
    }
  }

  Future<dynamic> changeFileNameOnly(dynamic file, String newFileName) async {
    try {
      // On web platforms, XFile renaming is not supported, return as-is
      if (kIsWeb || file is XFile) {
        log("Web platform or XFile detected: Skipping file rename");
        return file;
      }

      // Mobile platform File handling
      if (file is File) {
        if (!await file.exists()) {
          throw Exception("File tidak ditemukan: ${file.path}");
        }

        var path = file.path;
        var lastSeparator = path.lastIndexOf(Platform.pathSeparator);
        var newPath = path.substring(0, lastSeparator + 1) + newFileName;

        // Gunakan copy + delete untuk menghindari race condition
        // Copy file ke lokasi baru
        File oldFile = File(newPath);
        if (await oldFile.exists()) {
          await oldFile.delete();
        }

        File copiedFile = await file.copy(newPath);

        // Hapus file asli setelah copy berhasil
        await file.delete();

        return copiedFile;
      }

      // Fallback: return original file
      return file;
    } catch (e) {
      log("Error saat rename file: $e");
      // Kembalikan file asli jika terjadi error
      return file;
    }
  }

  // Upload image to server using API class
  Future<void> uploadImage(dynamic imageFile, String type) async {
    try {
      // Add to pending photos if not web platform
      if (!kIsWeb && _pendingPhotoService != null && imageFile != null) {
        final imagePath = imageFile.path ?? '';
        final formId = baseController.formId.value;
        if (imagePath.isNotEmpty && formId.isNotEmpty) {
          await _pendingPhotoService!.addPendingPhoto(
            localPath: imagePath,
            type: type,
            formId: formId,
          );
          log('Added photo to pending list: $type at $imagePath');
        }
      }

      // Set upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = true;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = true;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = true;
          pasFotoUploadProgress.value = 0.0;
          break;
      }

      // Call API upload function
      final result = await api.uploadRecruitmentImage(
        imageFile: imageFile,
        type: type,
        onProgress: (percent) {
          // Update progress
          switch (type) {
            case kPhotoTypeKtp:
              ktpUploadProgress.value = percent;
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUploadProgress.value = percent;
              break;
            case kPhotoTypePasFoto:
              pasFotoUploadProgress.value = percent;
              break;
          }
        },
      );
      if (result != null && result['success'] == true) {
        final uploadedUrl = result['url'] as String?;
        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          // Store the URL
          switch (type) {
            case kPhotoTypeKtp:
              ktpUrl.value = uploadedUrl;
              ktpImageError.value = '';
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUrl.value = uploadedUrl;
              selfieKtpImageError.value = '';
              break;
            case kPhotoTypePasFoto:
              pasFotoUrl.value = uploadedUrl;
              pasFotoImageError.value = '';
              break;
          }
          log("Upload berhasil untuk $type: $uploadedUrl");

          // Remove from pending photos on successful upload
          if (!kIsWeb && _pendingPhotoService != null && imageFile != null) {
            final imagePath = imageFile.path ?? '';
            if (imagePath.isNotEmpty) {
              final pendingPhotos =
                  _pendingPhotoService!.pendingPhotos
                      .where(
                        (photo) =>
                            photo.localPath == imagePath && photo.type == type,
                      )
                      .toList();
              for (final photo in pendingPhotos) {
                await _pendingPhotoService!.removePendingPhoto(photo.id);
              }
              log(
                'Removed photo from pending list after successful upload: $type',
              );
            }
          }
        }
      } else {
        final message = result?['message'] ?? 'Upload gagal';
        log("Upload gagal untuk $type: $message");
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
    } finally {
      // Reset upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = false;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = false;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = false;
          pasFotoUploadProgress.value = 0.0;
          break;
      }
    }
  }

  // Clear image method
  void clearImage(String type) async {
    switch (type) {
      case kPhotoTypeKtp:
        await ImageFileHelper.safeDeleteImageFile(ktpImage.value);
        ktpImage.value = null;
        ktpUrl.value = '';
        break;
      case kPhotoTypeSelfieKtp:
        await ImageFileHelper.safeDeleteImageFile(selfieKtpImage.value);
        selfieKtpImage.value = null;
        selfieKtpUrl.value = '';
        break;
      case kPhotoTypePasFoto:
        await ImageFileHelper.safeDeleteImageFile(pasFotoImage.value);
        pasFotoImage.value = null;
        pasFotoUrl.value = '';
        break;
    }
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Populate form data from model (from Firestore)
  void populateFormData(RecruitmentFormModel formData) {
    // Isi Form Verification dengan data - RxString
    if (formData.formStatus != 'draft') {
      recruiterName.value = formData.recruiterName ?? '';
      recruiterId.value = formData.recruiterId ?? '';
      recruiterBranch.value = formData.recruiterBranch ?? '';
      recruiterCode.value = formData.recruiterCode ?? '';
      recruiterLevel.value = formData.recruiterLevel ?? '';
      recruiterPhoto.value = formData.recruiterPhoto ?? '';
    }
    candidateLevelController.text =
        formData.candidateLevel == '' ? 'BP' : formData.candidateLevel ?? 'BP';
    candidateBranchController.text = formData.candidateBranch ?? '';
    candidateBranchText.value = formData.candidateBranch ?? '';
    candidateBranchCode.value = formData.candidateBranchCode ?? 0;

    // Try to restore branch code if it was lost during auto-save
    if (candidateBranchCode.value == 0 &&
        candidateBranchController.text.isNotEmpty) {
      Future.delayed(Duration(milliseconds: 500), () {
        _restoreBranchCodeIfValid();
      });
    }

    // Load image URLs
    ktpUrl.value = formData.ktpImageUrl ?? '';
    selfieKtpUrl.value = formData.selfieKtpImageUrl ?? '';
    pasFotoUrl.value = formData.pasFotoImageUrl ?? '';

    // Load image files from local paths if available (for offline mode - mobile only)
    if (!kIsWeb) {
      _loadImageFromPath(formData.ktpImagePath, kPhotoTypeKtp);
      _loadImageFromPath(formData.selfieKtpImagePath, kPhotoTypeSelfieKtp);
      _loadImageFromPath(formData.pasFotoImagePath, kPhotoTypePasFoto);
    }
  }

  // Load image from local path if available (for offline mode)
  Future<void> _loadImageFromPath(String? imagePath, String type) async {
    if (imagePath == null || imagePath.isEmpty) return;

    try {
      if (kIsWeb) {
        // On web, we can't load from local paths, skip this
        return;
      }

      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        // Set the image file to display it
        switch (type) {
          case kPhotoTypeKtp:
            ktpImage.value = imageFile;
            break;
          case kPhotoTypeSelfieKtp:
            selfieKtpImage.value = imageFile;
            break;
          case kPhotoTypePasFoto:
            pasFotoImage.value = imageFile;
            break;
        }
        log('Loaded image from local path for $type: $imagePath');
      } else {
        log('Image file not found at path for $type: $imagePath');
      }
    } catch (e) {
      log('Error loading image from path for $type: $e');
    }
  }

  // Check if all photos are uploaded (have URLs)
  bool areAllPhotosUploaded() {
    return ktpUrl.value.isNotEmpty &&
        selfieKtpUrl.value.isNotEmpty &&
        pasFotoUrl.value.isNotEmpty;
  }

  // Upload all pending photos before submit
  Future<bool> uploadPendingPhotos() async {
    try {
      setLoading(true);
      bool allUploaded = true;

      // Upload KTP if not uploaded yet
      if (ktpImage.value != null && ktpUrl.value.isEmpty) {
        log('Uploading KTP photo...');
        await uploadImage(ktpImage.value, kPhotoTypeKtp);
        if (ktpUrl.value.isEmpty) allUploaded = false;
      }

      // Upload Selfie KTP if not uploaded yet
      if (selfieKtpImage.value != null && selfieKtpUrl.value.isEmpty) {
        log('Uploading Selfie KTP photo...');
        await uploadImage(selfieKtpImage.value, kPhotoTypeSelfieKtp);
        if (selfieKtpUrl.value.isEmpty) allUploaded = false;
      }

      // Upload Pas Foto if not uploaded yet
      if (pasFotoImage.value != null && pasFotoUrl.value.isEmpty) {
        log('Uploading Pas Foto photo...');
        await uploadImage(pasFotoImage.value, kPhotoTypePasFoto);
        if (pasFotoUrl.value.isEmpty) allUploaded = false;
      }
      setLoading(false);
      return allUploaded;
    } catch (e) {
      log('Error uploading pending photos: $e');
      setLoading(false);
      return false;
    }
  }

  // Populate form data from API model (from approval page)
  void populateFormDataFromApi(RecruitmentApiModel apiData) {
    // Isi Form Verification dengan data dari API
    recruiterName.value = apiData.recruiterName ?? '';
    recruiterId.value = apiData.recruiter?.agentCode ?? '';
    recruiterBranch.value = apiData.branch?.branchName ?? '';
    recruiterCode.value = apiData.recruiterCode ?? '';
    recruiterLevel.value = apiData.positionLevel ?? '';
    recruiterPhoto.value = ''; // API model tidak memiliki recruiter photo
    candidateLevelController.text = apiData.positionLevel ?? 'BP';
    candidateBranchController.text = apiData.branch?.branchName ?? '';
    candidateBranchText.value = apiData.branch?.branchName ?? '';
    candidateBranchCode.value = apiData.branch?.id ?? 0;

    // Load image URLs dari API
    ktpUrl.value = apiData.ktpPhoto ?? '';
    selfieKtpUrl.value = apiData.selfiePhoto ?? '';
    pasFotoUrl.value = apiData.passPhoto ?? '';
  }

  // Validate form verification
  bool validateForm() {
    bool isValid = true;

    // Trim all text fields before validation
    _trimAllTextFields();

    // Clear previous errors
    ktpImageError.value = '';
    selfieKtpImageError.value = '';
    pasFotoImageError.value = '';
    candidateLevelError.value = '';
    candidateBranchError.value = '';

    // Validate KTP image
    if (ktpImage.value == null && ktpUrl.value.isEmpty) {
      ktpImageError.value = 'Foto KTP tidak boleh kosong';
      isValid = false;
    }

    // Validate Selfie KTP image
    if (selfieKtpImage.value == null && selfieKtpUrl.value.isEmpty) {
      selfieKtpImageError.value = 'Selfie dengan KTP tidak boleh kosong';
      isValid = false;
    }

    // Validate Pas Foto image
    if (pasFotoImage.value == null && pasFotoUrl.value.isEmpty) {
      pasFotoImageError.value = 'Pas foto tidak boleh kosong';
      isValid = false;
    }
    // Validate candidate level
    final levelError = FormValidation.validateRequired(
      candidateLevelController.text,
      'Level keagenan kandidat',
    );
    if (levelError != null) {
      candidateLevelError.value = levelError;
      isValid = false;
    }

    // Validate candidate branch
    final branchError = FormValidation.validateRequired(
      candidateBranchController.text,
      'Kantor cabang',
    );
    if (branchError != null) {
      candidateBranchError.value = branchError;
      isValid = false;
    } else {
      // Additional validation: ensure branch exists in list and has valid code
      final branchValidationError = _validateBranchSelection();
      if (branchValidationError != null) {
        candidateBranchError.value = branchValidationError;
        isValid = false;
      }
    }

    return isValid;
  }

  // Validate branch selection - ensure text exists in list and has valid code
  String? _validateBranchSelection() {
    final branchText = candidateBranchController.text.trim();
    final branchCode = candidateBranchCode.value;

    // Try to restore branch code if it's 0 but text is valid
    if (branchCode == 0) {
      _restoreBranchCodeIfValid();
    }

    // Check if branch code is valid (not 0) after restoration attempt
    if (candidateBranchCode.value == 0) {
      return 'Silakan pilih kantor cabang dari daftar yang tersedia';
    }

    // Check if branch text exists in allBranchList
    if (allBranchList.isNotEmpty) {
      final matchingBranch =
          allBranchList.where((branch) {
            return branch.branchName?.toLowerCase() == branchText.toLowerCase();
          }).toList();

      if (matchingBranch.isEmpty) {
        return 'Kantor cabang tidak valid. Silakan pilih dari daftar yang tersedia';
      }

      // Check if the selected branch code matches any branch with the same name
      final branchWithMatchingCode =
          matchingBranch.where((branch) {
            return branch.id == branchCode;
          }).toList();

      if (branchWithMatchingCode.isEmpty) {
        return 'Kantor cabang tidak sesuai. Silakan pilih ulang dari daftar';
      }
    }

    return null; // Validation passed
  }

  // Validate branch selection in real-time (called when user finishes typing)
  void validateBranchSelectionRealTime() {
    final validationError = _validateBranchSelection();
    candidateBranchError.value = validationError ?? '';
  }

  // Restore branch code if text matches a valid branch (for auto-save recovery)
  void _restoreBranchCodeIfValid() {
    if (candidateBranchCode.value == 0 &&
        candidateBranchController.text.isNotEmpty &&
        allBranchList.isNotEmpty) {
      // Find matching branch by name
      try {
        final matchingBranch = allBranchList.firstWhere(
          (branch) =>
              branch.branchName?.toLowerCase() ==
              candidateBranchController.text.toLowerCase(),
        );

        // Restore the branch code
        candidateBranchCode.value = matchingBranch.id ?? 0;
        log(
          'Branch code restored: ${matchingBranch.id} for ${matchingBranch.branchName}',
        );
      } catch (e) {
        // No matching branch found, keep code as 0
        log('No matching branch found for: ${candidateBranchController.text}');
      }
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk FormVerification text fields
    candidateLevelController.addListener(_onFormChanged);
    candidateBranchController.addListener(_onFormChanged);

    // Tambahkan listener untuk perubahan selection
    candidateBranchCode.listen((_) => _onFormChanged());
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Setup trim listeners untuk text fields
  void setupTrimListeners() {
    // Trim listeners sudah tidak diperlukan karena trim akan dilakukan
    // pada onEditingComplete di UI atau saat submit form
  }

  // Helper method untuk trim text field saat editing selesai
  void trimTextFieldOnComplete(TextEditingController controller) {
    final text = controller.text;
    final trimmedText = text.trim();

    if (text != trimmedText) {
      controller.text = trimmedText;
      // Set cursor ke akhir text setelah trim
      controller.selection = TextSelection.collapsed(
        offset: trimmedText.length,
      );
    }
  }

  // Method untuk trim semua text fields sebelum validasi
  void _trimAllTextFields() {
    // Trim verification fields
    candidateLevelController.text = candidateLevelController.text.trim();
    candidateBranchController.text = candidateBranchController.text.trim();
  }

  @override
  void onClose() {
    candidateLevelController.dispose();
    candidateBranchController.dispose();
    candidateBranchCode.value = 0;
    candidateBranchText.value = '';
    super.onClose();
  }
}
