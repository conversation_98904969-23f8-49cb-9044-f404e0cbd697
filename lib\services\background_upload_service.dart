import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_verification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_recruitment_form_controller.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/services/auto_save_service.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/utils/api.dart';
import 'package:pdl_superapp/utils/keys.dart';

class BackgroundUploadService extends GetxService {
  Timer? _uploadTimer;
  late PendingPhotoService _pendingPhotoService;
  late Api _api;
  NetworkController? _networkController;
  AutoSaveService? _autoSaveService;

  // Upload configuration
  static const int _uploadIntervalSeconds = 30; // Check every 30 seconds
  static const int _maxUploadAttempts = 3;
  static const int _cooldownMinutes = 5;

  // Status tracking
  final RxBool isUploading = false.obs;
  final RxInt totalPendingPhotos = 0.obs;
  final RxInt uploadedPhotosCount = 0.obs;

  // Debug mode
  final RxBool debugMode = false.obs;

  // Network status tracking
  final RxBool wasOffline = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeServices();
    _startBackgroundUpload();
    log('BackgroundUploadService initialized');
  }

  @override
  void onClose() {
    _stopBackgroundUpload();
    super.onClose();
  }

  /// Initialize required services
  Future<void> _initializeServices() async {
    try {
      _pendingPhotoService = Get.find<PendingPhotoService>();
    } catch (e) {
      _pendingPhotoService = Get.put(PendingPhotoService());
      await _pendingPhotoService.onInit();
    }

    _api = Api();

    // Initialize AutoSaveService
    try {
      _autoSaveService = Get.find<AutoSaveService>();
    } catch (e) {
      try {
        _autoSaveService = Get.put(AutoSaveService());
        await _autoSaveService!.onInit();
      } catch (createError) {
        // Failed to create AutoSaveService
      }
    }

    if (!kIsWeb) {
      try {
        _networkController = Get.find<NetworkController>();
        // Listen to network changes for debug notifications
        _networkController!.isConnected.listen((isConnected) {
          _onNetworkStatusChanged(isConnected);
        });
      } catch (e) {
        log(
          'NetworkController not found, background upload will work without network status',
        );
      }
    }

    // Listen to pending photos changes
    _pendingPhotoService.pendingPhotos.listen((photos) {
      totalPendingPhotos.value = photos.length;
    });
  }

  /// Start the background upload timer
  void _startBackgroundUpload() {
    _stopBackgroundUpload(); // Stop any existing timer

    _uploadTimer = Timer.periodic(Duration(seconds: _uploadIntervalSeconds), (
      timer,
    ) {
      _performBackgroundUpload();
    });

    // Perform initial upload check
    _performBackgroundUpload();

    log(
      'Background upload service started (interval: ${_uploadIntervalSeconds}s)',
    );
  }

  /// Stop the background upload timer
  void _stopBackgroundUpload() {
    _uploadTimer?.cancel();
    _uploadTimer = null;
    log('Background upload service stopped');
  }

  /// Handle network status changes
  void _onNetworkStatusChanged(bool isConnected) {
    if (!isConnected) {
      // Going offline
      wasOffline.value = true;
      if (debugMode.value) {
        _showDebugPopup(
          'Network Status',
          'Offline - Background upload paused',
          isError: true,
        );
      }
      log('Network went offline - background upload paused');
    } else if (wasOffline.value) {
      // Coming back online
      wasOffline.value = false;
      if (debugMode.value) {
        _showDebugPopup(
          'Network Status',
          'Online - Checking for pending photos...',
          isError: false,
        );
      }
      log('Network back online - triggering immediate upload check');

      // Trigger immediate upload when coming back online
      Future.delayed(Duration(seconds: 2), () {
        _performBackgroundUpload();
      });
    }
  }

  /// Show debug popup
  void _showDebugPopup(String title, String message, {bool isError = false}) {
    // if (!debugMode.value) return;

    try {
      Get.snackbar(
        title,
        message,
        backgroundColor:
            isError
                ? Colors.red.withValues(alpha: 0.8)
                : Colors.green.withValues(alpha: 0.8),
        colorText: Colors.white,
        duration: Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
        margin: EdgeInsets.all(8),
      );
    } catch (e) {
      log('Failed to show debug popup: $e');
    }
  }

  /// Perform background upload of pending photos
  Future<void> _performBackgroundUpload() async {
    if (isUploading.value) {
      log('here Upload already in progress, skipping...');
      return;
    }

    // Check network connectivity (skip on web)
    if (!kIsWeb && _networkController != null) {
      if (!_networkController!.isConnected.value) {
        log('here No internet connection, skipping background upload');
        return;
      }
    }
    final photosToUpload = _pendingPhotoService.getPhotosForRetry(
      maxAttempts: _maxUploadAttempts,
      cooldownMinutes: _cooldownMinutes,
    );

    if (photosToUpload.isEmpty) {
      return; // No photos to upload
    }

    log(
      'DEBUG: Starting background upload for ${photosToUpload.length} photos',
    );
    if (debugMode.value) {
      _showDebugPopup(
        'Background Upload',
        'Uploading ${photosToUpload.length} pending photos...',
      );
    }

    isUploading.value = true;

    try {
      int successCount = 0;
      for (final photo in photosToUpload) {
        final success = await _uploadSinglePhoto(photo);
        if (success) successCount++;

        // Small delay between uploads to avoid overwhelming the server
        await Future.delayed(Duration(milliseconds: 500));
      }

      if (debugMode.value && successCount > 0) {
        _showDebugPopup(
          'Upload Complete',
          'Successfully uploaded $successCount/${photosToUpload.length} photos',
        );
      }
    } catch (e) {
      log('Error during background upload: $e');
      if (debugMode.value) {
        _showDebugPopup(
          'Upload Error',
          'Error during background upload: $e',
          isError: true,
        );
      }
    } finally {
      isUploading.value = false;
      log('Background upload completed');
    }
  }

  /// Upload a single photo
  Future<bool> _uploadSinglePhoto(PendingPhoto photo) async {
    try {
      log(
        'Attempting to upload photo: ${photo.type} (attempt ${photo.uploadAttempts + 1})',
      );

      // Update attempt count
      await _pendingPhotoService.updateUploadAttempt(photo.id);

      // Check if file still exists (skip on web)
      if (!kIsWeb) {
        final file = File(photo.localPath);
        if (!await file.exists()) {
          log(
            'File no longer exists: ${photo.localPath}, removing from pending',
          );
          await _pendingPhotoService.removePendingPhoto(photo.id);
          return false;
        }
      }

      // Prepare file for upload
      dynamic fileToUpload;
      if (kIsWeb) {
        // On web, we need to handle this differently
        // For now, skip web uploads in background service
        log(
          'Web platform detected, skipping background upload for: ${photo.type}',
        );
        return false;
      } else {
        fileToUpload = File(photo.localPath);
      }

      // Perform the upload
      final result = await _api.uploadRecruitmentImage(
        imageFile: fileToUpload,
        type: photo.type,
        onProgress: (percent) {
          // Silent upload, no progress updates needed
        },
      );

      if (result != null && result['success'] == true) {
        // Upload successful
        final uploadedUrl = result['url'] as String?;
        log('Successfully uploaded photo: ${photo.type}');

        // Update URL in controller if available
        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          _updateControllerUrl(photo.type, uploadedUrl);

          // Trigger auto-save form after successful upload
          await _triggerAutoSaveForm(photo.formId, photo.type, uploadedUrl);
        }

        await _pendingPhotoService.updateUploadAttempt(photo.id, success: true);
        uploadedPhotosCount.value++;

        if (debugMode.value) {
          _showDebugPopup(
            'Photo Uploaded',
            'Successfully uploaded ${photo.type}',
          );
        }
        return true;
      } else {
        // Upload failed
        log('Failed to upload photo: ${photo.type}');

        // If max attempts reached, remove from pending
        if (photo.uploadAttempts + 1 >= _maxUploadAttempts) {
          log(
            'Max upload attempts reached for photo: ${photo.type}, removing from pending',
          );
          await _pendingPhotoService.removePendingPhoto(photo.id);
        }
        return false;
      }
    } catch (e) {
      log('Error uploading photo ${photo.type}: $e');

      // If max attempts reached, remove from pending
      if (photo.uploadAttempts + 1 >= _maxUploadAttempts) {
        log(
          'Max upload attempts reached for photo: ${photo.type}, removing from pending',
        );
        await _pendingPhotoService.removePendingPhoto(photo.id);
      }
      return false;
    }
  }

  /// Manually trigger upload for all pending photos
  Future<void> uploadAllPendingPhotos() async {
    if (isUploading.value) {
      log('Upload already in progress');
      return;
    }

    final allPendingPhotos = _pendingPhotoService.pendingPhotos.toList();
    if (allPendingPhotos.isEmpty) {
      log('No pending photos to upload');
      return;
    }

    log('Manually uploading ${allPendingPhotos.length} pending photos');
    isUploading.value = true;

    try {
      for (final photo in allPendingPhotos) {
        await _uploadSinglePhoto(photo);
        await Future.delayed(Duration(milliseconds: 500));
      }
    } catch (e) {
      log('Error during manual upload: $e');
    } finally {
      isUploading.value = false;
    }
  }

  /// Force upload a specific photo by ID
  Future<bool> uploadPhotoById(String photoId) async {
    final photo = _pendingPhotoService.pendingPhotos.firstWhereOrNull(
      (p) => p.id == photoId,
    );

    if (photo == null) {
      log('Photo with ID $photoId not found in pending list');
      return false;
    }

    try {
      await _uploadSinglePhoto(photo);
      return true;
    } catch (e) {
      log('Error uploading photo by ID $photoId: $e');
      return false;
    }
  }

  /// Get upload statistics
  Map<String, dynamic> getUploadStats() {
    return {
      'totalPending': totalPendingPhotos.value,
      'uploaded': uploadedPhotosCount.value,
      'isUploading': isUploading.value,
      'uploadInterval': _uploadIntervalSeconds,
      'maxAttempts': _maxUploadAttempts,
    };
  }

  /// Pause background upload
  void pauseBackgroundUpload() {
    _stopBackgroundUpload();
    log('Background upload paused');
  }

  /// Resume background upload
  void resumeBackgroundUpload() {
    _startBackgroundUpload();
    log('Background upload resumed');
  }

  /// Check if background upload is active
  bool get isBackgroundUploadActive => _uploadTimer?.isActive ?? false;

  /// Enable debug mode to show popup notifications
  void enableDebugMode() {
    debugMode.value = true;
    _showDebugPopup('Debug Mode', 'Background upload debug mode enabled');
    log('Background upload debug mode enabled');
  }

  /// Disable debug mode
  void disableDebugMode() {
    debugMode.value = false;
    log('Background upload debug mode disabled');
  }

  /// Toggle debug mode
  void toggleDebugMode() {
    if (debugMode.value) {
      disableDebugMode();
    } else {
      enableDebugMode();
    }
  }

  /// Force trigger background upload for debugging
  void forceUploadCheck() {
    _performBackgroundUpload();
  }

  /// Get detailed status for debugging
  Map<String, dynamic> getDetailedStatus() {
    final status = {
      'isBackgroundUploadActive': isBackgroundUploadActive,
      'debugMode': debugMode.value,
      'isUploading': isUploading.value,
      'totalPendingPhotos': totalPendingPhotos.value,
      'uploadedPhotosCount': uploadedPhotosCount.value,
      'wasOffline': wasOffline.value,
      'uploadInterval': _uploadIntervalSeconds,
      'maxAttempts': _maxUploadAttempts,
      'cooldownMinutes': _cooldownMinutes,
    };

    return status;
  }

  /// Update URL in controller after successful background upload
  void _updateControllerUrl(String photoType, String uploadedUrl) {
    try {
      // Try to find FormVerificationController
      try {
        final controller = Get.find<FormVerificationController>();
        switch (photoType) {
          case kPhotoTypeKtp:
            controller.ktpUrl.value = uploadedUrl;
            break;
          case kPhotoTypeSelfieKtp:
            controller.selfieKtpUrl.value = uploadedUrl;
            break;
          case kPhotoTypePasFoto:
            controller.pasFotoUrl.value = uploadedUrl;
            break;
        }
      } catch (e) {
        // FormVerificationController not found
      }

      // Try to find PublicFormVerificationController as fallback
      try {
        final publicController = Get.find<PublicFormVerificationController>();
        switch (photoType) {
          case kPhotoTypeKtp:
            publicController.ktpUrl.value = uploadedUrl;
            break;
          case kPhotoTypeSelfieKtp:
            publicController.selfieKtpUrl.value = uploadedUrl;
            break;
          case kPhotoTypePasFoto:
            publicController.pasFotoUrl.value = uploadedUrl;
            break;
        }
      } catch (e) {
        // PublicFormVerificationController not found
      }
    } catch (e) {
      // Error updating controller URL
    }
  }

  /// Trigger auto-save form after successful photo upload
  Future<void> _triggerAutoSaveForm(
    String formId,
    String photoType,
    String photoUrl,
  ) async {
    try {
      // Use AutoSaveService for independent auto-save
      if (_autoSaveService != null) {
        // Check if form can be auto-saved
        final canSave = await _autoSaveService!.canAutoSaveForm(formId);
        if (!canSave) {
          return;
        }

        // Perform auto-save with photo URL
        final saveResult = await _autoSaveService!.autoSaveFormAfterPhotoUpload(
          formId: formId,
          photoType: photoType,
          photoUrl: photoUrl,
        );

        if (debugMode.value) {
          _showDebugPopup(
            'Auto-Save',
            saveResult
                ? 'Form auto-saved after photo upload'
                : 'Auto-save failed',
          );
        }
        return;
      }

      // Fallback: Try to find active controllers (only if AutoSaveService failed)
      try {
        final controller = Get.find<RecruitmentFormController>();
        if (controller.formId.value == formId &&
            !controller.isFormSubmitted.value) {
          await controller.saveFormData(isSubmit: false);
          return;
        }
      } catch (e) {
        // RecruitmentFormController fallback failed
      }

      try {
        final publicController = Get.find<PublicRecruitmentFormController>();
        if (publicController.formId.value == formId) {
          await publicController.saveFormData(isSubmit: false);
          return;
        }
      } catch (e) {
        // PublicRecruitmentFormController fallback failed
      }
    } catch (e) {
      // Error triggering auto-save
    }
  }
}
